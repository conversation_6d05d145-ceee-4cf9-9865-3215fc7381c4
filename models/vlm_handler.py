import torch
from transformers import AutoProcessor, AutoModelForVision2Seq, pipeline
from PIL import Image
import logging
import os
from typing import Optional, Dict, Any, Union
import io
import base64

logger = logging.getLogger(__name__)

class VLMHandler:
    """Handler for Vision Language Models from Hugging Face."""
    
    def __init__(self, model_name: str, device: str = "auto", cache_dir: Optional[str] = None):
        """
        Initialize the VLM handler.
        
        Args:
            model_name: Name of the model to load
            device: Device to run the model on ('auto', 'cpu', 'cuda')
            cache_dir: Directory to cache models
        """
        self.model_name = model_name
        self.cache_dir = cache_dir
        self.device = self._get_device(device)
        self.model = None
        self.processor = None
        self.pipeline = None
        
        logger.info(f"Initializing VLM handler with model: {model_name}")
        logger.info(f"Using device: {self.device}")
        
    def _get_device(self, device: str) -> str:
        """Determine the appropriate device to use."""
        if device == "auto":
            if torch.cuda.is_available():
                return "cuda"
            else:
                return "cpu"
        return device
    
    def load_model(self) -> None:
        """Load the VLM model and processor."""
        try:
            logger.info(f"Loading model: {self.model_name}")
            
            # Try to use pipeline first (simpler approach)
            if "blip2" in self.model_name.lower():
                self.pipeline = pipeline(
                    "image-to-text",
                    model=self.model_name,
                    device=0 if self.device == "cuda" else -1,
                    cache_dir=self.cache_dir
                )
            else:
                # For more advanced VLMs like SmolVLM, LLaVA
                self.processor = AutoProcessor.from_pretrained(
                    self.model_name,
                    cache_dir=self.cache_dir
                )
                self.model = AutoModelForVision2Seq.from_pretrained(
                    self.model_name,
                    torch_dtype=torch.float16 if self.device == "cuda" else torch.float32,
                    device_map=self.device if self.device == "cuda" else None,
                    cache_dir=self.cache_dir
                )
                
                if self.device == "cpu":
                    self.model = self.model.to(self.device)
            
            logger.info("Model loaded successfully")
            
        except Exception as e:
            logger.error(f"Error loading model: {str(e)}")
            raise
    
    def process_image(self, image_input: Union[str, bytes, Image.Image]) -> Image.Image:
        """
        Process image input into PIL Image.
        
        Args:
            image_input: Can be file path, bytes, or PIL Image
            
        Returns:
            PIL Image object
        """
        if isinstance(image_input, Image.Image):
            return image_input
        elif isinstance(image_input, str):
            if image_input.startswith('data:image'):
                # Handle base64 encoded images
                header, data = image_input.split(',', 1)
                image_bytes = base64.b64decode(data)
                return Image.open(io.BytesIO(image_bytes))
            else:
                # Handle file path
                return Image.open(image_input)
        elif isinstance(image_input, bytes):
            return Image.open(io.BytesIO(image_input))
        else:
            raise ValueError(f"Unsupported image input type: {type(image_input)}")
    
    def generate_response(self, image: Union[str, bytes, Image.Image], 
                         prompt: str = "Describe this image in detail.") -> Dict[str, Any]:
        """
        Generate a response from the VLM.
        
        Args:
            image: Image input (file path, bytes, or PIL Image)
            prompt: Text prompt for the model
            
        Returns:
            Dictionary containing the response and metadata
        """
        try:
            if self.model is None and self.pipeline is None:
                self.load_model()
            
            # Process the image
            pil_image = self.process_image(image)
            
            if self.pipeline:
                # Use pipeline approach (for BLIP2)
                result = self.pipeline(pil_image)
                response_text = result[0]['generated_text'] if result else "No response generated"
            else:
                # Use processor + model approach (for SmolVLM, LLaVA)
                inputs = self.processor(
                    text=prompt,
                    images=pil_image,
                    return_tensors="pt"
                ).to(self.device)
                
                with torch.no_grad():
                    outputs = self.model.generate(
                        **inputs,
                        max_new_tokens=512,
                        do_sample=True,
                        temperature=0.7,
                        pad_token_id=self.processor.tokenizer.eos_token_id
                    )
                
                # Decode the response
                response_text = self.processor.decode(
                    outputs[0], 
                    skip_special_tokens=True
                )
                
                # Remove the input prompt from the response
                if prompt in response_text:
                    response_text = response_text.replace(prompt, "").strip()
            
            return {
                "success": True,
                "response": response_text,
                "model": self.model_name,
                "prompt": prompt
            }
            
        except Exception as e:
            logger.error(f"Error generating response: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "model": self.model_name,
                "prompt": prompt
            }
    
    def is_loaded(self) -> bool:
        """Check if the model is loaded."""
        return self.model is not None or self.pipeline is not None
