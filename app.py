from flask import Flask, request, jsonify, render_template, send_from_directory
import os
import logging
from werkzeug.utils import secure_filename
from PIL import Image
import io
import base64
from config import Config
from models.vlm_handler import VLMHandler

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = Flask(__name__)
app.config.from_object(Config)

# Global VLM handler
vlm_handler = None

def get_vlm_handler():
    """Get or create the VLM handler instance."""
    global vlm_handler
    if vlm_handler is None:
        vlm_handler = VLMHandler(
            model_name=Config.DEFAULT_MODEL,
            device=Config.DEVICE,
            cache_dir=Config.MODEL_CACHE_DIR
        )
    return vlm_handler

@app.route('/')
def index():
    """Serve the main web interface."""
    return render_template('index.html')

@app.route('/api/models', methods=['GET'])
def get_available_models():
    """Get list of available VLM models."""
    return jsonify({
        "success": True,
        "models": Config.AVAILABLE_MODELS,
        "current_model": Config.DEFAULT_MODEL
    })

@app.route('/api/vlm/analyze', methods=['POST'])
def analyze_image():
    """
    Analyze an image with the VLM.
    
    Expects:
    - image: file upload or base64 encoded image
    - prompt: text prompt (optional)
    - model: model name (optional)
    """
    try:
        # Get the prompt
        prompt = request.form.get('prompt', 'Describe this image in detail.')
        
        # Get the model (optional)
        model_name = request.form.get('model')
        if model_name and model_name in Config.AVAILABLE_MODELS:
            # Create a new handler for different model
            handler = VLMHandler(
                model_name=Config.AVAILABLE_MODELS[model_name],
                device=Config.DEVICE,
                cache_dir=Config.MODEL_CACHE_DIR
            )
        else:
            handler = get_vlm_handler()
        
        # Get the image
        image = None
        
        # Check for file upload
        if 'image' in request.files:
            file = request.files['image']
            if file and file.filename and Config.allowed_file(file.filename):
                # Read the image file
                image_bytes = file.read()
                image = Image.open(io.BytesIO(image_bytes))
            else:
                return jsonify({
                    "success": False,
                    "error": "Invalid or missing image file"
                }), 400
        
        # Check for base64 image in form data
        elif 'image_data' in request.form:
            image_data = request.form['image_data']
            if image_data.startswith('data:image'):
                image = image_data
            else:
                return jsonify({
                    "success": False,
                    "error": "Invalid image data format"
                }), 400
        
        # Check for JSON payload
        elif request.is_json:
            data = request.get_json()
            if 'image_data' in data:
                image = data['image_data']
            prompt = data.get('prompt', prompt)
        
        if image is None:
            return jsonify({
                "success": False,
                "error": "No image provided"
            }), 400
        
        # Generate response
        result = handler.generate_response(image, prompt)
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Error in analyze_image: {str(e)}")
        return jsonify({
            "success": False,
            "error": f"Internal server error: {str(e)}"
        }), 500

@app.route('/api/vlm/status', methods=['GET'])
def get_model_status():
    """Get the current model status."""
    try:
        handler = get_vlm_handler()
        return jsonify({
            "success": True,
            "model_loaded": handler.is_loaded(),
            "model_name": handler.model_name,
            "device": handler.device
        })
    except Exception as e:
        logger.error(f"Error getting model status: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/api/vlm/load', methods=['POST'])
def load_model():
    """Manually load the model."""
    try:
        handler = get_vlm_handler()
        if not handler.is_loaded():
            handler.load_model()
        
        return jsonify({
            "success": True,
            "message": "Model loaded successfully",
            "model_name": handler.model_name
        })
    except Exception as e:
        logger.error(f"Error loading model: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.errorhandler(413)
def too_large(e):
    """Handle file too large error."""
    return jsonify({
        "success": False,
        "error": "File too large. Maximum size is 16MB."
    }), 413

@app.errorhandler(404)
def not_found(e):
    """Handle 404 errors."""
    return jsonify({
        "success": False,
        "error": "Endpoint not found"
    }), 404

@app.errorhandler(500)
def internal_error(e):
    """Handle internal server errors."""
    return jsonify({
        "success": False,
        "error": "Internal server error"
    }), 500

if __name__ == '__main__':
    # Create necessary directories
    os.makedirs(Config.MODEL_CACHE_DIR, exist_ok=True)
    
    # Start the Flask app
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=Config.DEBUG
    )
