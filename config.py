import os

class Config:
    """Configuration settings for the VLM Flask application."""
    
    # Flask settings
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-change-in-production'
    DEBUG = os.environ.get('FLASK_DEBUG', 'False').lower() == 'true'
    
    # Model settings
    DEFAULT_MODEL = os.environ.get('VLM_MODEL', 'HuggingFaceTB/SmolVLM-Instruct')
    MODEL_CACHE_DIR = os.environ.get('MODEL_CACHE_DIR', './model_cache')
    
    # API settings
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB max file size
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp'}
    
    # Device settings
    DEVICE = os.environ.get('DEVICE', 'auto')  # 'auto', 'cpu', 'cuda'
    
    # Available models
    AVAILABLE_MODELS = {
        'smolvlm': 'HuggingFaceTB/SmolVLM-Instruct',
        'llava-1.5-7b': 'llava-hf/llava-1.5-7b-hf',
        'llava-1.5-13b': 'llava-hf/llava-1.5-13b-hf',
        'blip2-opt-2.7b': 'Salesforce/blip2-opt-2.7b',
        'blip2-flan-t5-xl': 'Salesforce/blip2-flan-t5-xl'
    }
    
    @staticmethod
    def allowed_file(filename):
        """Check if file extension is allowed."""
        return '.' in filename and \
               filename.rsplit('.', 1)[1].lower() in Config.ALLOWED_EXTENSIONS
