* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

.main-content {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.upload-section {
    margin-bottom: 25px;
}

.upload-area {
    border: 3px dashed #ddd;
    border-radius: 10px;
    padding: 40px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #fafafa;
}

.upload-area:hover {
    border-color: #667eea;
    background: #f0f4ff;
}

.upload-area.dragover {
    border-color: #667eea;
    background: #e8f0ff;
}

.upload-content .upload-icon {
    font-size: 3rem;
    margin-bottom: 15px;
}

.upload-content p {
    color: #666;
    font-size: 1.1rem;
}

.image-preview {
    position: relative;
    display: inline-block;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.image-preview img {
    max-width: 100%;
    max-height: 300px;
    display: block;
}

.remove-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(255,255,255,0.9);
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    cursor: pointer;
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.3s ease;
}

.remove-btn:hover {
    background: rgba(255,0,0,0.8);
    color: white;
}

.prompt-section, .model-section {
    margin-bottom: 20px;
}

.prompt-section label, .model-section label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
}

.prompt-section textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 14px;
    resize: vertical;
    transition: border-color 0.3s ease;
}

.prompt-section textarea:focus {
    outline: none;
    border-color: #667eea;
}

.model-section select {
    width: 100%;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 14px;
    background: white;
    transition: border-color 0.3s ease;
}

.model-section select:focus {
    outline: none;
    border-color: #667eea;
}

.action-section {
    text-align: center;
    margin-bottom: 25px;
}

.analyze-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 25px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 150px;
}

.analyze-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.analyze-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.result-section {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 25px;
}

.result-section h3 {
    margin-bottom: 15px;
    color: #333;
}

.result-content {
    background: white;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #667eea;
    line-height: 1.6;
}

.status-section {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.status-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.status-label {
    font-weight: 600;
    color: #666;
    font-size: 14px;
}

.status-value {
    color: #333;
    font-size: 16px;
}

.status-value.loading {
    color: #f39c12;
}

.status-value.loaded {
    color: #27ae60;
}

.status-value.error {
    color: #e74c3c;
}

footer {
    text-align: center;
    margin-top: 30px;
    color: white;
    opacity: 0.8;
}

@media (max-width: 600px) {
    .container {
        padding: 15px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .main-content {
        padding: 20px;
    }
    
    .status-section {
        grid-template-columns: 1fr;
    }
}
