<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vision Language Model - Flask App</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
</head>
<body>
    <div class="container">
        <header>
            <h1>🤖 Vision Language Model</h1>
            <p>Upload an image and ask questions about it using AI</p>
        </header>

        <div class="main-content">
            <div class="upload-section">
                <div class="upload-area" id="uploadArea">
                    <div class="upload-content">
                        <div class="upload-icon">📷</div>
                        <p>Drag & drop an image here or click to browse</p>
                        <input type="file" id="imageInput" accept="image/*" hidden>
                    </div>
                </div>
                
                <div class="image-preview" id="imagePreview" style="display: none;">
                    <img id="previewImg" src="" alt="Preview">
                    <button class="remove-btn" id="removeImage">✕</button>
                </div>
            </div>

            <div class="prompt-section">
                <label for="promptInput">Ask a question about the image:</label>
                <textarea 
                    id="promptInput" 
                    placeholder="Describe this image in detail..."
                    rows="3"
                >Describe this image in detail.</textarea>
            </div>

            <div class="model-section">
                <label for="modelSelect">Choose Model:</label>
                <select id="modelSelect">
                    <option value="">Loading models...</option>
                </select>
            </div>

            <div class="action-section">
                <button id="analyzeBtn" class="analyze-btn" disabled>
                    <span class="btn-text">Analyze Image</span>
                    <span class="btn-loading" style="display: none;">Analyzing...</span>
                </button>
            </div>

            <div class="result-section" id="resultSection" style="display: none;">
                <h3>Analysis Result:</h3>
                <div class="result-content" id="resultContent"></div>
            </div>

            <div class="status-section">
                <div class="status-item">
                    <span class="status-label">Model Status:</span>
                    <span class="status-value" id="modelStatus">Checking...</span>
                </div>
                <div class="status-item">
                    <span class="status-label">Current Model:</span>
                    <span class="status-value" id="currentModel">Loading...</span>
                </div>
            </div>
        </div>

        <footer>
            <p>Powered by Hugging Face Transformers and Flask</p>
        </footer>
    </div>

    <script src="{{ url_for('static', filename='script.js') }}"></script>
</body>
</html>
